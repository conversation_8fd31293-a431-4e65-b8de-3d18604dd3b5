import { useState, useRef } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import ConsentFormPreview from './ConsentFormPreview';

// Set up PDF.js worker - using local file
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

export default function ConsentCreator() {
  // Initial empty consent form structure following the schema
  const emptyConsentForm = {
    metadata: {
      id: "",
      version: "1.0",
      fileNamePrefix: "",
      description: ""
    },
    header: {
      title: "",
      subtitle: "",
      organization: ""
    },
    sections: [],
    signature: {
      required: true,
      label: "Participant Signature:",
      participantNameLabel: "Participant Name:",
      dateLabel: "Date:",
      signatureLineText: "Signature"
    },
    styling: {
      primaryColor: "#2c5aa0",
      fontFamily: "Arial, sans-serif"
    }
  };

  // Section type options
  const sectionTypes = [
    { id: "text", name: "Text Section", description: "Plain text content" },
    { id: "list", name: "List Section", description: "Bulleted or numbered list" },
    { id: "contact", name: "Contact Section", description: "Contact information" },
    { id: "nested-list", name: "Nested List", description: "List with sub-items" }
  ];

  // State management
  const [consentForm, setConsentForm] = useState(emptyConsentForm);
  const [currentSection, setCurrentSection] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showFormPreview, setShowFormPreview] = useState(false);
  const [jsonOutput, setJsonOutput] = useState("");
  const [importError, setImportError] = useState("");
  const [textInput, setTextInput] = useState("");
  const [showTextProcessor, setShowTextProcessor] = useState(false);

  // Refs
  const fileInputRef = useRef(null);
  const pdfInputRef = useRef(null);

  // Generate unique ID
  const generateId = () => {
    return 'section_' + Math.random().toString(36).substr(2, 9);
  };

  // Validate consent form against schema
  const validateConsentForm = (form) => {
    const errors = [];

    // Validate metadata
    if (!form.metadata) {
      errors.push("Metadata is required");
    } else {
      if (!form.metadata.id) errors.push("Metadata ID is required");
      if (!form.metadata.version) errors.push("Metadata version is required");
      if (!form.metadata.fileNamePrefix) errors.push("Metadata fileNamePrefix is required");
    }

    // Validate header
    if (!form.header) {
      errors.push("Header is required");
    } else {
      if (!form.header.title) errors.push("Header title is required");
    }

    // Validate sections
    if (!Array.isArray(form.sections)) {
      errors.push("Sections must be an array");
    } else {
      form.sections.forEach((section, index) => {
        if (!section.id) errors.push(`Section ${index + 1}: ID is required`);
        if (!section.title) errors.push(`Section ${index + 1}: Title is required`);
        if (!section.type) errors.push(`Section ${index + 1}: Type is required`);
        if (!['text', 'list', 'contact', 'nested-list'].includes(section.type)) {
          errors.push(`Section ${index + 1}: Invalid type "${section.type}"`);
        }

        // Validate content based on type
        if (section.type === 'text' && typeof section.content !== 'string') {
          errors.push(`Section ${index + 1}: Text content must be a string`);
        }
        if (section.type === 'list' && !Array.isArray(section.content)) {
          errors.push(`Section ${index + 1}: List content must be an array`);
        }
        if (section.type === 'contact' && typeof section.content !== 'object') {
          errors.push(`Section ${index + 1}: Contact content must be an object`);
        }
        if (section.type === 'nested-list' && !Array.isArray(section.content)) {
          errors.push(`Section ${index + 1}: Nested list content must be an array`);
        }
      });
    }

    // Validate signature
    if (!form.signature) {
      errors.push("Signature configuration is required");
    } else {
      if (typeof form.signature.required !== 'boolean') {
        errors.push("Signature required field must be a boolean");
      }
    }

    return errors;
  };

  // Save the entire consent form
  const saveConsentForm = () => {
    // Validate the form
    const validationErrors = validateConsentForm(consentForm);

    if (validationErrors.length > 0) {
      setImportError("Validation errors:\n" + validationErrors.join("\n"));
      return;
    }

    const formattedForm = {
      "$schema": "http://json-schema.org/draft-07/schema#",
      ...consentForm
    };

    setJsonOutput(JSON.stringify(formattedForm, null, 2));
    setShowPreview(true);
    setImportError("");
  };

  // Add a new section
  const addSection = (type) => {
    const newSection = {
      id: generateId(),
      title: "New Section",
      type: type,
      content: getDefaultContent(type),
      emphasis: false,
      required: true
    };

    setConsentForm({
      ...consentForm,
      sections: [...consentForm.sections, newSection]
    });

    setCurrentSection(consentForm.sections.length);
  };

  // Get default content based on section type
  const getDefaultContent = (type) => {
    switch (type) {
      case 'text':
        return "Enter your text content here...";
      case 'list':
        return ["List item 1", "List item 2"];
      case 'contact':
        return {
          description: "Contact information",
          phone: "",
          email: "",
          address: ""
        };
      case 'nested-list':
        return [
          {
            text: "Main item 1",
            subItems: ["Sub-item 1", "Sub-item 2"]
          }
        ];
      default:
        return "";
    }
  };

  // Update section
  const updateSection = (index, field, value) => {
    const updatedSections = [...consentForm.sections];
    updatedSections[index][field] = value;

    setConsentForm({
      ...consentForm,
      sections: updatedSections
    });
  };

  // Delete section
  const deleteSection = (index) => {
    const updatedSections = consentForm.sections.filter((_, i) => i !== index);

    setConsentForm({
      ...consentForm,
      sections: updatedSections
    });

    if (currentSection === index) {
      setCurrentSection(null);
    } else if (currentSection > index) {
      setCurrentSection(currentSection - 1);
    }
  };

  // Advanced text processing to intelligently parse consent form content
  const processTextInput = () => {
    if (!textInput.trim()) return;

    const lines = textInput.split('\n').map(line => line.trim()).filter(line => line);
    const newSections = [];

    // Try to extract header information first
    const firstFewLines = lines.slice(0, 5);
    let headerInfo = { title: "", subtitle: "", organization: "" };
    let contentStartIndex = 0;

    // Look for title in first few lines (usually the longest or most prominent line)
    for (let i = 0; i < Math.min(3, firstFewLines.length); i++) {
      const line = firstFewLines[i];
      if (line.length > 10 && (
        line === line.toUpperCase() ||
        line.includes('CONSENT') ||
        line.includes('AGREEMENT') ||
        line.includes('FORM')
      )) {
        if (!headerInfo.title) {
          headerInfo.title = line;
          contentStartIndex = i + 1;
        } else if (!headerInfo.subtitle) {
          headerInfo.subtitle = line;
          contentStartIndex = i + 1;
        }
      }
    }

    // Update header if we found information
    if (headerInfo.title) {
      setConsentForm(prev => ({
        ...prev,
        header: {
          ...prev.header,
          title: headerInfo.title,
          subtitle: headerInfo.subtitle || prev.header.subtitle
        }
      }));
    }

    // Process the remaining content
    const contentLines = lines.slice(contentStartIndex);
    let currentSection = null;
    let currentContent = [];

    const isHeader = (line) => {
      return (
        // All caps and reasonable length
        (line === line.toUpperCase() && line.length > 3 && line.length < 80) ||
        // Ends with colon
        line.endsWith(':') ||
        // Numbered sections
        /^\d+\./.test(line) ||
        // Roman numerals
        /^[IVX]+\./.test(line) ||
        // Letter sections
        /^[A-Z]\./.test(line) ||
        // Common section headers
        /^(purpose|procedure|risk|benefit|confidential|contact|signature|participant)/i.test(line)
      );
    };

    const isList = (line) => {
      return (
        // Bullet points
        /^[•·\-\*]/.test(line) ||
        // Numbered lists
        /^\d+[\.\)]/.test(line) ||
        // Letter lists
        /^[a-z][\.\)]/.test(line)
      );
    };

    const detectSectionType = (title, content) => {
      const titleLower = title.toLowerCase();
      const contentText = Array.isArray(content) ? content.join(' ') : content;
      const contentLower = contentText.toLowerCase();

      if (titleLower.includes('contact') || contentLower.includes('phone') || contentLower.includes('email')) {
        return 'contact';
      }

      // Check if content has list-like structure
      const listItems = content.filter(line => isList(line));
      if (listItems.length > 1) {
        return 'list';
      }

      return 'text';
    };

    contentLines.forEach((line, index) => {
      if (isHeader(line)) {
        // Save previous section
        if (currentSection && currentContent.length > 0) {
          const sectionType = detectSectionType(currentSection.title, currentContent);

          if (sectionType === 'list') {
            // Convert to list format
            const listItems = currentContent
              .filter(item => isList(item))
              .map(item => item.replace(/^[•·\-\*\d+\.\)a-z\.\)]\s*/, '').trim());

            currentSection.content = listItems.length > 0 ? listItems : currentContent;
          } else if (sectionType === 'contact') {
            // Try to extract contact information
            const contactInfo = {
              description: currentContent.find(line => !line.includes('@') && !line.match(/\d{3}[-\.\s]?\d{3}[-\.\s]?\d{4}/)) || "",
              phone: currentContent.find(line => line.match(/\d{3}[-\.\s]?\d{3}[-\.\s]?\d{4}/)) || "",
              email: currentContent.find(line => line.includes('@')) || "",
              address: currentContent.find(line => line.match(/\d+.*street|avenue|road|blvd|dr/i)) || ""
            };
            currentSection.content = contactInfo;
          } else {
            // Text content
            currentSection.content = currentContent.join('\n\n');
          }

          currentSection.type = sectionType;
          newSections.push(currentSection);
        }

        // Start new section
        currentSection = {
          id: generateId(),
          title: line.replace(/[:\.]/g, '').trim(),
          type: 'text',
          content: "",
          emphasis: line === line.toUpperCase(),
          required: true
        };
        currentContent = [];
      } else if (line.length > 0) {
        // Add to current content
        currentContent.push(line);
      }
    });

    // Don't forget the last section
    if (currentSection && currentContent.length > 0) {
      const sectionType = detectSectionType(currentSection.title, currentContent);

      if (sectionType === 'list') {
        const listItems = currentContent
          .filter(item => isList(item))
          .map(item => item.replace(/^[•·\-\*\d+\.\)a-z\.\)]\s*/, '').trim());

        currentSection.content = listItems.length > 0 ? listItems : currentContent;
      } else if (sectionType === 'contact') {
        const contactInfo = {
          description: currentContent.find(line => !line.includes('@') && !line.match(/\d{3}[-\.\s]?\d{3}[-\.\s]?\d{4}/)) || "",
          phone: currentContent.find(line => line.match(/\d{3}[-\.\s]?\d{3}[-\.\s]?\d{4}/)) || "",
          email: currentContent.find(line => line.includes('@')) || "",
          address: currentContent.find(line => line.match(/\d+.*street|avenue|road|blvd|dr/i)) || ""
        };
        currentSection.content = contactInfo;
      } else {
        currentSection.content = currentContent.join('\n\n');
      }

      currentSection.type = sectionType;
      newSections.push(currentSection);
    }

    // If no sections were created, create a single text section
    if (newSections.length === 0 && contentLines.length > 0) {
      newSections.push({
        id: generateId(),
        title: "Content",
        type: 'text',
        content: contentLines.join('\n\n'),
        emphasis: false,
        required: true
      });
    }

    // Add the processed sections
    setConsentForm(prev => ({
      ...prev,
      sections: [...prev.sections, ...newSections]
    }));

    setTextInput("");
    setShowTextProcessor(false);
  };

  // Process PDF file
  const processPdfFile = async (file) => {
    try {
      setImportError("Processing PDF file...");

      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      let fullText = "";

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        // Combine text items from the page
        const pageText = textContent.items
          .map(item => item.str)
          .join(' ')
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();

        if (pageText) {
          fullText += pageText + '\n\n';
        }
      }

      if (fullText.trim()) {
        // Use the existing text processing logic
        setTextInput(fullText);
        setShowTextProcessor(true);
        setImportError("");
      } else {
        setImportError("No text content found in the PDF file.");
      }

    } catch (error) {
      console.error("PDF processing error:", error);
      setImportError("Failed to process PDF file. Please ensure it's a valid PDF with text content, or try copying and pasting the text instead.");
    }
  };

  // Handle PDF file upload
  const handlePdfUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      setImportError("Please select a valid PDF file.");
      return;
    }

    processPdfFile(file);
    event.target.value = null; // Reset file input
  };

  // Reset form
  const resetForm = () => {
    if (window.confirm("Are you sure you want to start a new consent form? All current data will be lost.")) {
      setConsentForm(emptyConsentForm);
      setCurrentSection(null);
      setShowPreview(false);
      setJsonOutput("");
      setImportError("");
    }
  };

  // Download JSON
  const downloadJson = () => {
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(jsonOutput);
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `${consentForm.metadata.fileNamePrefix || "consent-form"}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  // Handle JSON file import
  const handleJsonImport = (event) => {
    setImportError("");
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target.result;
        const importedData = JSON.parse(content);

        // Validate the imported data
        const validationErrors = validateConsentForm(importedData);

        if (validationErrors.length > 0) {
          setImportError("Invalid consent form format:\n" + validationErrors.join("\n"));
          return;
        }

        setConsentForm({
          ...emptyConsentForm,
          ...importedData
        });

        setCurrentSection(null);
        setShowPreview(true);
        setJsonOutput(JSON.stringify(importedData, null, 2));
        event.target.value = null;
      } catch (error) {
        setImportError("Failed to parse JSON file. Please ensure it's a valid JSON file.");
        console.error("Import error:", error);
      }
    };

    reader.onerror = () => {
      setImportError("Error reading file. Please try again.");
    };

    reader.readAsText(file);
  };

  return (
    <div className="max-w-6xl mx-auto p-4">
      <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 rounded-lg shadow-lg mb-6">
        <h1 className="text-3xl font-bold text-center text-white">Consent Creator</h1>
        <p className="text-center text-white opacity-90">Create and convert consent forms to structured JSON</p>
      </div>

      {/* Metadata Section */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800">Form Metadata</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-gray-700 mb-2">Form ID *</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.metadata.id}
              onChange={(e) => setConsentForm({
                ...consentForm,
                metadata: { ...consentForm.metadata, id: e.target.value }
              })}
              placeholder="e.g., consent-form-v1"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Version</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.metadata.version}
              onChange={(e) => setConsentForm({
                ...consentForm,
                metadata: { ...consentForm.metadata, version: e.target.value }
              })}
              placeholder="1.0"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">File Name Prefix</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.metadata.fileNamePrefix}
              onChange={(e) => setConsentForm({
                ...consentForm,
                metadata: { ...consentForm.metadata, fileNamePrefix: e.target.value }
              })}
              placeholder="consent-form"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Description</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.metadata.description}
              onChange={(e) => setConsentForm({
                ...consentForm,
                metadata: { ...consentForm.metadata, description: e.target.value }
              })}
              placeholder="Optional description"
            />
          </div>
        </div>
      </div>

      {/* Header Section */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800">Form Header</h2>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-gray-700 mb-2">Title *</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.header.title}
              onChange={(e) => setConsentForm({
                ...consentForm,
                header: { ...consentForm.header, title: e.target.value }
              })}
              placeholder="Informed Consent Form"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Subtitle</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.header.subtitle}
              onChange={(e) => setConsentForm({
                ...consentForm,
                header: { ...consentForm.header, subtitle: e.target.value }
              })}
              placeholder="Research Study Participation"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Organization</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.header.organization}
              onChange={(e) => setConsentForm({
                ...consentForm,
                header: { ...consentForm.header, organization: e.target.value }
              })}
              placeholder="University Research Department"
            />
          </div>
        </div>
      </div>

      {/* Text Input Processor */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Content Input</h2>
          <div className="flex space-x-2">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
              onClick={() => setShowTextProcessor(!showTextProcessor)}
            >
              {showTextProcessor ? "Hide Text Processor" : "Process Text"}
            </button>
            <button
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition"
              onClick={() => pdfInputRef.current.click()}
            >
              Upload PDF
            </button>
          </div>
        </div>

        {showTextProcessor && (
          <div className="border p-4 rounded-md bg-gray-50 mb-4">
            <h3 className="font-semibold mb-2">Paste Your Consent Form Text</h3>
            <p className="text-sm text-gray-600 mb-3">
              Paste your consent form text below. The system will automatically detect sections and structure the content.
            </p>
            <textarea
              className="w-full h-64 px-3 py-2 border rounded-md resize-y"
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="Paste your consent form text here..."
            />
            <div className="flex justify-end mt-3 space-x-2">
              <button
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                onClick={() => {
                  setTextInput("");
                  setShowTextProcessor(false);
                }}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                onClick={processTextInput}
              >
                Process Text
              </button>
            </div>
          </div>
        )}

        <input
          type="file"
          ref={pdfInputRef}
          accept=".pdf"
          className="hidden"
          onChange={handlePdfUpload}
        />
      </div>

      {/* Sections Management */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Sections ({consentForm.sections.length})</h2>
          <div className="flex flex-wrap gap-2">
            {sectionTypes.map(type => (
              <button
                key={type.id}
                className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                onClick={() => addSection(type.id)}
                title={type.description}
              >
                Add {type.name}
              </button>
            ))}
          </div>
        </div>

        {consentForm.sections.length === 0 ? (
          <div className="text-center py-8 text-gray-500 bg-gray-50 rounded border">
            <div className="px-4">
              No sections yet. Add sections using the buttons above or process text content.
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {consentForm.sections.map((section, index) => (
              <div
                key={section.id}
                className={`border rounded-lg overflow-hidden ${currentSection === index ? 'border-blue-500 shadow-md' : 'border-gray-200'}`}
              >
                <div className="flex justify-between items-center bg-gray-50 p-4 border-b">
                  <div className="flex-1 flex items-center space-x-3">
                    <span className="bg-blue-100 text-blue-800 text-xs font-medium py-1 px-2 rounded">
                      {sectionTypes.find(t => t.id === section.type)?.name || section.type}
                    </span>
                    <input
                      type="text"
                      className="flex-1 px-2 py-1 border rounded"
                      value={section.title}
                      onChange={(e) => updateSection(index, 'title', e.target.value)}
                    />
                    <label className="flex items-center text-sm">
                      <input
                        type="checkbox"
                        checked={section.emphasis}
                        onChange={(e) => updateSection(index, 'emphasis', e.target.checked)}
                        className="mr-1"
                      />
                      Emphasis
                    </label>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      className={`px-3 py-1 rounded ${currentSection === index ? 'bg-blue-100 text-blue-700' : 'bg-gray-200 hover:bg-gray-300'}`}
                      onClick={() => setCurrentSection(currentSection === index ? null : index)}
                    >
                      {currentSection === index ? 'Close' : 'Edit'}
                    </button>
                    <button
                      className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                      onClick={() => deleteSection(index)}
                    >
                      Delete
                    </button>
                  </div>
                </div>

                {currentSection === index && (
                  <div className="p-4">
                    {/* Text Section Editor */}
                    {section.type === 'text' && (
                      <div>
                        <label className="block text-gray-700 mb-2 font-medium">Content</label>
                        <textarea
                          className="w-full h-32 px-3 py-2 border rounded-md resize-y"
                          value={section.content}
                          onChange={(e) => updateSection(index, 'content', e.target.value)}
                          placeholder="Enter the text content for this section..."
                        />
                      </div>
                    )}

                    {/* List Section Editor */}
                    {section.type === 'list' && (
                      <div>
                        <label className="block text-gray-700 mb-2 font-medium">List Items</label>
                        <div className="space-y-2 mb-3">
                          {Array.isArray(section.content) && section.content.map((item, itemIndex) => (
                            <div key={itemIndex} className="flex items-center space-x-2">
                              <input
                                type="text"
                                className="flex-1 px-3 py-2 border rounded-md"
                                value={typeof item === 'string' ? item : item.text || ''}
                                onChange={(e) => {
                                  const newContent = [...section.content];
                                  newContent[itemIndex] = e.target.value;
                                  updateSection(index, 'content', newContent);
                                }}
                                placeholder={`List item ${itemIndex + 1}`}
                              />
                              <button
                                className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                                onClick={() => {
                                  const newContent = section.content.filter((_, i) => i !== itemIndex);
                                  updateSection(index, 'content', newContent);
                                }}
                              >
                                Remove
                              </button>
                            </div>
                          ))}
                        </div>
                        <button
                          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                          onClick={() => {
                            const newContent = Array.isArray(section.content) ? [...section.content] : [];
                            newContent.push("");
                            updateSection(index, 'content', newContent);
                          }}
                        >
                          Add Item
                        </button>
                      </div>
                    )}

                    {/* Contact Section Editor */}
                    {section.type === 'contact' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-gray-700 mb-2 font-medium">Description</label>
                          <textarea
                            className="w-full h-20 px-3 py-2 border rounded-md resize-y"
                            value={section.content?.description || ''}
                            onChange={(e) => updateSection(index, 'content', {
                              ...section.content,
                              description: e.target.value
                            })}
                            placeholder="Contact description..."
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 mb-2 font-medium">Phone</label>
                          <input
                            type="text"
                            className="w-full px-3 py-2 border rounded-md"
                            value={section.content?.phone || ''}
                            onChange={(e) => updateSection(index, 'content', {
                              ...section.content,
                              phone: e.target.value
                            })}
                            placeholder="Phone number"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 mb-2 font-medium">Email</label>
                          <input
                            type="email"
                            className="w-full px-3 py-2 border rounded-md"
                            value={section.content?.email || ''}
                            onChange={(e) => updateSection(index, 'content', {
                              ...section.content,
                              email: e.target.value
                            })}
                            placeholder="Email address"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-700 mb-2 font-medium">Address</label>
                          <textarea
                            className="w-full h-20 px-3 py-2 border rounded-md resize-y"
                            value={section.content?.address || ''}
                            onChange={(e) => updateSection(index, 'content', {
                              ...section.content,
                              address: e.target.value
                            })}
                            placeholder="Address"
                          />
                        </div>
                      </div>
                    )}

                    {/* Nested List Section Editor */}
                    {section.type === 'nested-list' && (
                      <div>
                        <label className="block text-gray-700 mb-2 font-medium">Nested List Items</label>
                        <div className="space-y-4 mb-3">
                          {Array.isArray(section.content) && section.content.map((item, itemIndex) => (
                            <div key={itemIndex} className="border rounded-md p-3 bg-gray-50">
                              <div className="flex items-center space-x-2 mb-2">
                                <input
                                  type="text"
                                  className="flex-1 px-3 py-2 border rounded-md bg-white"
                                  value={item.text || ''}
                                  onChange={(e) => {
                                    const newContent = [...section.content];
                                    newContent[itemIndex] = { ...item, text: e.target.value };
                                    updateSection(index, 'content', newContent);
                                  }}
                                  placeholder={`Main item ${itemIndex + 1}`}
                                />
                                <button
                                  className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                                  onClick={() => {
                                    const newContent = section.content.filter((_, i) => i !== itemIndex);
                                    updateSection(index, 'content', newContent);
                                  }}
                                >
                                  Remove
                                </button>
                              </div>

                              <div className="ml-4">
                                <label className="block text-gray-600 mb-1 text-sm">Sub-items:</label>
                                <div className="space-y-1">
                                  {item.subItems && item.subItems.map((subItem, subIndex) => (
                                    <div key={subIndex} className="flex items-center space-x-2">
                                      <input
                                        type="text"
                                        className="flex-1 px-2 py-1 border rounded text-sm bg-white"
                                        value={subItem}
                                        onChange={(e) => {
                                          const newContent = [...section.content];
                                          const newSubItems = [...item.subItems];
                                          newSubItems[subIndex] = e.target.value;
                                          newContent[itemIndex] = { ...item, subItems: newSubItems };
                                          updateSection(index, 'content', newContent);
                                        }}
                                        placeholder={`Sub-item ${subIndex + 1}`}
                                      />
                                      <button
                                        className="px-1 py-1 bg-red-400 text-white rounded hover:bg-red-500 text-xs"
                                        onClick={() => {
                                          const newContent = [...section.content];
                                          const newSubItems = item.subItems.filter((_, i) => i !== subIndex);
                                          newContent[itemIndex] = { ...item, subItems: newSubItems };
                                          updateSection(index, 'content', newContent);
                                        }}
                                      >
                                        ×
                                      </button>
                                    </div>
                                  ))}
                                </div>
                                <button
                                  className="mt-1 px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-xs"
                                  onClick={() => {
                                    const newContent = [...section.content];
                                    const newSubItems = [...(item.subItems || []), ""];
                                    newContent[itemIndex] = { ...item, subItems: newSubItems };
                                    updateSection(index, 'content', newContent);
                                  }}
                                >
                                  Add Sub-item
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                        <button
                          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                          onClick={() => {
                            const newContent = Array.isArray(section.content) ? [...section.content] : [];
                            newContent.push({ text: "", subItems: [""] });
                            updateSection(index, 'content', newContent);
                          }}
                        >
                          Add Main Item
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Signature Settings */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800">Signature Settings</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="signatureRequired"
              checked={consentForm.signature.required}
              onChange={(e) => setConsentForm({
                ...consentForm,
                signature: { ...consentForm.signature, required: e.target.checked }
              })}
              className="mr-2"
            />
            <label htmlFor="signatureRequired" className="text-gray-700">Signature Required</label>
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Signature Label</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.signature.label}
              onChange={(e) => setConsentForm({
                ...consentForm,
                signature: { ...consentForm.signature, label: e.target.value }
              })}
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Participant Name Label</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.signature.participantNameLabel}
              onChange={(e) => setConsentForm({
                ...consentForm,
                signature: { ...consentForm.signature, participantNameLabel: e.target.value }
              })}
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Date Label</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.signature.dateLabel}
              onChange={(e) => setConsentForm({
                ...consentForm,
                signature: { ...consentForm.signature, dateLabel: e.target.value }
              })}
            />
          </div>
        </div>
      </div>

      {/* Styling Settings */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800">Styling</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-gray-700 mb-2">Primary Color</label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                className="w-12 h-10 border rounded"
                value={consentForm.styling.primaryColor}
                onChange={(e) => setConsentForm({
                  ...consentForm,
                  styling: { ...consentForm.styling, primaryColor: e.target.value }
                })}
              />
              <input
                type="text"
                className="flex-1 px-3 py-2 border rounded-md"
                value={consentForm.styling.primaryColor}
                onChange={(e) => setConsentForm({
                  ...consentForm,
                  styling: { ...consentForm.styling, primaryColor: e.target.value }
                })}
              />
            </div>
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Font Family</label>
            <select
              className="w-full px-3 py-2 border rounded-md"
              value={consentForm.styling.fontFamily}
              onChange={(e) => setConsentForm({
                ...consentForm,
                styling: { ...consentForm.styling, fontFamily: e.target.value }
              })}
            >
              <option value="Arial, sans-serif">Arial</option>
              <option value="Times New Roman, serif">Times New Roman</option>
              <option value="Helvetica, sans-serif">Helvetica</option>
              <option value="Georgia, serif">Georgia</option>
              <option value="Verdana, sans-serif">Verdana</option>
            </select>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap justify-center gap-4 mb-6">
        <button
          className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
          onClick={saveConsentForm}
        >
          Generate JSON
        </button>
        <button
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          onClick={() => setShowPreview(!showPreview)}
        >
          {showPreview ? "Hide JSON" : "Preview JSON"}
        </button>
        <button
          className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
          onClick={() => setShowFormPreview(true)}
        >
          Preview Form
        </button>
        {jsonOutput && (
          <button
            className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition"
            onClick={downloadJson}
          >
            Download JSON
          </button>
        )}
        <button
          className="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition"
          onClick={() => fileInputRef.current.click()}
        >
          Import JSON
        </button>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleJsonImport}
          accept=".json"
          className="hidden"
        />
        <button
          className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
          onClick={resetForm}
        >
          New Form
        </button>
      </div>

      {/* Error Message */}
      {importError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p className="font-bold">Error</p>
          <p>{importError}</p>
        </div>
      )}

      {/* JSON Preview */}
      {showPreview && (
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-bold mb-4 text-gray-800">JSON Output</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded-md overflow-x-auto">
            <pre className="text-sm whitespace-pre-wrap">{jsonOutput || "No JSON generated yet. Click 'Generate JSON' first."}</pre>
          </div>
        </div>
      )}

      {/* Form Preview Modal */}
      {showFormPreview && (
        <ConsentFormPreview
          consentForm={consentForm}
          onClose={() => setShowFormPreview(false)}
        />
      )}
    </div>
  );
}
