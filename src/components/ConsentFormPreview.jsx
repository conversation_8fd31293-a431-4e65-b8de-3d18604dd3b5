import { useState } from 'react';

export default function ConsentFormPreview({ consentForm, onClose }) {
  const [formData, setFormData] = useState({});
  const [signature, setSignature] = useState('');
  const [participantName, setParticipantName] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);

  const handleInputChange = (sectionId, value) => {
    setFormData(prev => ({
      ...prev,
      [sectionId]: value
    }));
  };

  const renderSection = (section) => {
    switch (section.type) {
      case 'text':
        return (
          <div className="mb-6">
            <h3 className={`text-lg font-medium mb-3 ${section.emphasis ? 'font-bold text-xl' : ''}`}>
              {section.title}
            </h3>
            <div className="text-gray-700 whitespace-pre-wrap leading-relaxed">
              {section.content}
            </div>
          </div>
        );

      case 'list':
        return (
          <div className="mb-6">
            <h3 className={`text-lg font-medium mb-3 ${section.emphasis ? 'font-bold text-xl' : ''}`}>
              {section.title}
            </h3>
            <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
              {Array.isArray(section.content) && section.content.map((item, index) => (
                <li key={index} className="leading-relaxed">
                  {typeof item === 'string' ? item : item.text || ''}
                </li>
              ))}
            </ul>
          </div>
        );

      case 'contact':
        return (
          <div className="mb-6">
            <h3 className={`text-lg font-medium mb-3 ${section.emphasis ? 'font-bold text-xl' : ''}`}>
              {section.title}
            </h3>
            <div className="bg-gray-50 p-4 rounded-md">
              {section.content?.description && (
                <p className="text-gray-700 mb-3">{section.content.description}</p>
              )}
              <div className="space-y-2">
                {section.content?.phone && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600 w-20">Phone:</span>
                    <span className="text-gray-700">{section.content.phone}</span>
                  </div>
                )}
                {section.content?.email && (
                  <div className="flex items-center">
                    <span className="font-medium text-gray-600 w-20">Email:</span>
                    <span className="text-gray-700">{section.content.email}</span>
                  </div>
                )}
                {section.content?.address && (
                  <div className="flex items-start">
                    <span className="font-medium text-gray-600 w-20">Address:</span>
                    <span className="text-gray-700 whitespace-pre-wrap">{section.content.address}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'nested-list':
        return (
          <div className="mb-6">
            <h3 className={`text-lg font-medium mb-3 ${section.emphasis ? 'font-bold text-xl' : ''}`}>
              {section.title}
            </h3>
            <div className="space-y-3">
              {Array.isArray(section.content) && section.content.map((item, index) => (
                <div key={index}>
                  <div className="font-medium text-gray-800 mb-1">
                    {item.text}
                  </div>
                  {item.subItems && item.subItems.length > 0 && (
                    <ul className="list-disc list-inside ml-6 space-y-1">
                      {item.subItems.map((subItem, subIndex) => (
                        <li key={subIndex} className="text-gray-700">
                          {subItem}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Preview Header */}
        <div className="flex justify-between items-center p-4 border-b bg-gray-50">
          <h2 className="text-xl font-bold text-gray-800">Consent Form Preview</h2>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition"
          >
            Close Preview
          </button>
        </div>

        {/* Form Content */}
        <div className="overflow-y-auto p-6" style={{ 
          fontFamily: consentForm.styling?.fontFamily || 'Arial, sans-serif',
          maxHeight: 'calc(90vh - 120px)'
        }}>
          {/* Header */}
          <div className="text-center mb-8 pb-6 border-b">
            {consentForm.header?.organization && (
              <div className="text-sm text-gray-600 mb-2">
                {consentForm.header.organization}
              </div>
            )}
            <h1 
              className="text-3xl font-bold mb-2"
              style={{ color: consentForm.styling?.primaryColor || '#2c5aa0' }}
            >
              {consentForm.header?.title || 'Consent Form'}
            </h1>
            {consentForm.header?.subtitle && (
              <div className="text-lg text-gray-600">
                {consentForm.header.subtitle}
              </div>
            )}
          </div>

          {/* Sections */}
          <div className="space-y-6">
            {consentForm.sections?.map((section) => (
              <div key={section.id}>
                {renderSection(section)}
              </div>
            ))}
          </div>

          {/* Signature Section */}
          {consentForm.signature?.required && (
            <div className="mt-8 pt-6 border-t">
              <h3 className="text-lg font-medium mb-4">Signature</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {consentForm.signature.participantNameLabel || 'Participant Name:'}
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={participantName}
                    onChange={(e) => setParticipantName(e.target.value)}
                    placeholder="Enter your full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {consentForm.signature.dateLabel || 'Date:'}
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={date}
                    onChange={(e) => setDate(e.target.value)}
                  />
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {consentForm.signature.label || 'Participant Signature:'}
                </label>
                <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
                  <input
                    type="text"
                    className="w-full bg-transparent border-none outline-none text-lg font-signature"
                    value={signature}
                    onChange={(e) => setSignature(e.target.value)}
                    placeholder="Type your signature here"
                    style={{ fontFamily: 'cursive' }}
                  />
                  <div className="text-xs text-gray-500 mt-2">
                    {consentForm.signature.signatureLineText || 'Signature'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Submit Button (for preview purposes) */}
          <div className="mt-8 text-center">
            <button
              className="px-8 py-3 text-white rounded-lg font-medium"
              style={{ backgroundColor: consentForm.styling?.primaryColor || '#2c5aa0' }}
              onClick={() => alert('This is a preview. In a real form, this would submit the consent.')}
            >
              Submit Consent
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
