import { useState, useRef } from 'react';
import ConsentCreator from './ConsentCreator';

export default function QuestionnaireBuilder() {
  const [activeTab, setActiveTab] = useState('questionnaire');
  // Initial empty questionnaire structure
  const emptyQuestionnaire = {
    name: "",
    banner: "",
    legend: [
      {
        rows: [],
        headers: []
      }
    ],
    sections: []
  };

  // Question type options
  const questionTypes = [
    { id: "single", name: "Select One" },
    { id: "multiple", name: "Select Multiple" },
    { id: "freeform", name: "Free Form" }
  ];

  // State management
  const [questionnaire, setQuestionnaire] = useState(emptyQuestionnaire);
  const [currentSection, setCurrentSection] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [jsonOutput, setJsonOutput] = useState("");
  const [editingLegend, setEditingLegend] = useState(false);
  const [newHeaderName, setNewHeaderName] = useState("");
  const [newRowValues, setNewRowValues] = useState([]);
  const [importError, setImportError] = useState("");

  // Refs
  const fileInputRef = useRef(null);

  // Editing states
  const [editingQuestion, setEditingQuestion] = useState(null);
  const [newOption, setNewOption] = useState({ title: "", value: 0, message: "" });

  // Save the entire questionnaire
  const saveQuestionnaire = () => {
    const formattedQuestionnaire = {
      ...questionnaire,
      sections: questionnaire.sections.map(section => ({
        ...section,
        questions: section.questions.map(question => ({
          ...question,
          questions: question.type === "freeform" ? [] : question.questions
        }))
      }))
    };

    setJsonOutput(JSON.stringify(formattedQuestionnaire, null, 2));
    setShowPreview(true);
  };

  // Add a new section
  const addSection = () => {
    const newSection = {
      type: "",
      title: "New Section",
      complete: false,
      questions: []
    };

    setQuestionnaire({
      ...questionnaire,
      sections: [...questionnaire.sections, newSection]
    });

    setCurrentSection(questionnaire.sections.length);
  };

  // Edit section title
  const updateSectionTitle = (index, newTitle) => {
    const updatedSections = [...questionnaire.sections];
    updatedSections[index].title = newTitle;

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });
  };

  // Delete a section
  const deleteSection = (index) => {
    const updatedSections = questionnaire.sections.filter((_, i) => i !== index);

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });

    if (currentSection === index) {
      setCurrentSection(null);
    } else if (currentSection > index) {
      setCurrentSection(currentSection - 1);
    }
  };

  // Add a new question to the current section
  const addQuestion = (sectionIndex, type) => {
    const newQuestion = {
      level: 0,
      title: "New Question",
      value: 0,
      message: "New Question\n",
      complete: false,
      question: "",
      type: type,
      questions: type === "freeform" ? [] : []
    };

    const updatedSections = [...questionnaire.sections];
    updatedSections[sectionIndex].questions.push(newQuestion);

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });

    // Start editing the new question
    setEditingQuestion({
      sectionIndex,
      questionIndex: updatedSections[sectionIndex].questions.length - 1
    });
  };

  // Update question title
  const updateQuestionTitle = (sectionIndex, questionIndex, newTitle) => {
    const updatedSections = [...questionnaire.sections];
    updatedSections[sectionIndex].questions[questionIndex].title = newTitle;
    updatedSections[sectionIndex].questions[questionIndex].message = newTitle + "\n";

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });
  };

  // Delete a question
  const deleteQuestion = (sectionIndex, questionIndex) => {
    const updatedSections = [...questionnaire.sections];
    updatedSections[sectionIndex].questions = updatedSections[sectionIndex].questions.filter((_, i) => i !== questionIndex);

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });

    if (editingQuestion && editingQuestion.sectionIndex === sectionIndex && editingQuestion.questionIndex === questionIndex) {
      setEditingQuestion(null);
    }
  };

  // Add an option to a question
  const addOptionToQuestion = (sectionIndex, questionIndex) => {
    if (!newOption.title) return;

    const updatedSections = [...questionnaire.sections];
    const currentOptions = updatedSections[sectionIndex].questions[questionIndex].questions || [];

    // Add the new option
    const optionToAdd = {
      type: "",
      level: 3,
      score: 0,
      title: newOption.title,
      value: newOption.value || currentOptions.length,
      message: newOption.message || newOption.title,
      questions: []
    };

    updatedSections[sectionIndex].questions[questionIndex].questions = [...currentOptions, optionToAdd];

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });

    // Reset the new option form
    setNewOption({ title: "", value: currentOptions.length + 1, message: "" });
  };

  // Remove an option from a question
  const removeOption = (sectionIndex, questionIndex, optionIndex) => {
    const updatedSections = [...questionnaire.sections];
    updatedSections[sectionIndex].questions[questionIndex].questions =
      updatedSections[sectionIndex].questions[questionIndex].questions.filter((_, i) => i !== optionIndex);

    setQuestionnaire({
      ...questionnaire,
      sections: updatedSections
    });
  };

  // Add a header to the legend
  const addHeader = () => {
    if (!newHeaderName) return;

    const updatedLegend = [...questionnaire.legend];
    updatedLegend[0].headers.push(newHeaderName);

    // Update all rows to have an empty value for the new column
    updatedLegend[0].rows = updatedLegend[0].rows.map(row => [...row, ""]);

    setQuestionnaire({
      ...questionnaire,
      legend: updatedLegend
    });

    setNewHeaderName("");
  };

  // Add a row to the legend
  const addLegendRow = () => {
    if (newRowValues.length !== questionnaire.legend[0].headers.length && questionnaire.legend[0].headers.length > 0) {
      alert("Please provide values for all headers");
      return;
    }

    const updatedLegend = [...questionnaire.legend];
    updatedLegend[0].rows.push(newRowValues.length > 0 ? newRowValues : Array(questionnaire.legend[0].headers.length).fill(""));

    setQuestionnaire({
      ...questionnaire,
      legend: updatedLegend
    });

    setNewRowValues([]);
  };

  // Update a legend cell
  const updateLegendCell = (rowIndex, colIndex, value) => {
    const updatedLegend = [...questionnaire.legend];
    updatedLegend[0].rows[rowIndex][colIndex] = value;

    setQuestionnaire({
      ...questionnaire,
      legend: updatedLegend
    });
  };

  // Update a header name
  const updateHeaderName = (index, value) => {
    const updatedLegend = [...questionnaire.legend];
    updatedLegend[0].headers[index] = value;

    setQuestionnaire({
      ...questionnaire,
      legend: updatedLegend
    });
  };

  // Remove a header from the legend
  const removeHeader = (index) => {
    const updatedLegend = [...questionnaire.legend];
    updatedLegend[0].headers = updatedLegend[0].headers.filter((_, i) => i !== index);
    updatedLegend[0].rows = updatedLegend[0].rows.map(row => row.filter((_, i) => i !== index));

    setQuestionnaire({
      ...questionnaire,
      legend: updatedLegend
    });
  };

  // Remove a row from the legend
  const removeLegendRow = (index) => {
    const updatedLegend = [...questionnaire.legend];
    updatedLegend[0].rows = updatedLegend[0].rows.filter((_, i) => i !== index);

    setQuestionnaire({
      ...questionnaire,
      legend: updatedLegend
    });
  };

  // Create a new empty questionnaire
  const resetQuestionnaire = () => {
    // eslint-disable-next-line no-restricted-globals
    if (confirm("Are you sure you want to start a new questionnaire? All current data will be lost.")) {
      setQuestionnaire(emptyQuestionnaire);
      setCurrentSection(null);
      setEditingQuestion(null);
      setShowPreview(false);
      setJsonOutput("");
      setEditingLegend(false);
    }
  };

  // Save to file
  const downloadJson = () => {
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(jsonOutput);
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `${questionnaire.name || "questionnaire"}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  // Trigger file input click
  const handleImportClick = () => {
    fileInputRef.current.click();
  };

  // Handle file selection
  const handleFileChange = (event) => {
    setImportError("");
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const content = e.target.result;
        const importedData = JSON.parse(content);

        // Basic validation
        if (!importedData.name || !Array.isArray(importedData.sections)) {
          setImportError("Invalid questionnaire format. The file must contain a valid questionnaire JSON structure.");
          return;
        }

        // Ensure the imported data has the correct structure
        const validatedData = {
          ...emptyQuestionnaire,  // Start with the empty structure
          ...importedData,        // Override with imported data
          // Ensure legend exists and has the correct structure
          legend: importedData.legend && Array.isArray(importedData.legend) && importedData.legend.length > 0
            ? importedData.legend
            : [{ headers: [], rows: [] }],
          // Ensure sections have the correct structure
          sections: Array.isArray(importedData.sections)
            ? importedData.sections.map(section => ({
                type: section.type || "",
                title: section.title || "Unnamed Section",
                complete: section.complete || false,
                questions: Array.isArray(section.questions)
                  ? section.questions.map(question => ({
                      level: question.level || 0,
                      title: question.title || "Unnamed Question",
                      value: question.value || 0,
                      message: question.message || "",
                      complete: question.complete || false,
                      question: question.question || "",
                      type: question.type || "single",
                      questions: Array.isArray(question.questions) ? question.questions : []
                    }))
                  : []
              }))
            : []
        };

        // Set the questionnaire state with validated data
        setQuestionnaire(validatedData);

        // Update UI state
        setCurrentSection(null);
        setEditingQuestion(null);
        setShowPreview(true);
        setJsonOutput(JSON.stringify(validatedData, null, 2));

        // Reset file input
        event.target.value = null;
      } catch (error) {
        setImportError("Failed to parse JSON file. Please ensure it's a valid JSON file.");
        console.error("Import error:", error);
      }
    };

    reader.onerror = () => {
      setImportError("Error reading file. Please try again.");
    };

    reader.readAsText(file);
  };

  if (activeTab === 'consent') {
    return <ConsentCreator />;
  }

  return (
    <div className="max-w-6xl mx-auto p-4">
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-lg shadow-lg mb-6">
        <h1 className="text-3xl font-bold text-center text-white">Assessment Builder</h1>
        <p className="text-center text-white opacity-90">Create custom assessments and consent forms</p>

        {/* Tab Navigation */}
        <div className="flex justify-center mt-4 space-x-4">
          <button
            className={`px-4 py-2 rounded-lg font-medium transition ${
              activeTab === 'questionnaire'
                ? 'bg-white text-blue-600'
                : 'bg-blue-500 text-white hover:bg-blue-400'
            }`}
            onClick={() => setActiveTab('questionnaire')}
          >
            Questionnaire Builder
          </button>
          <button
            className={`px-4 py-2 rounded-lg font-medium transition ${
              activeTab === 'consent'
                ? 'bg-white text-blue-600'
                : 'bg-blue-500 text-white hover:bg-blue-400'
            }`}
            onClick={() => setActiveTab('consent')}
          >
            Consent Creator
          </button>
        </div>
      </div>

      {/* Questionnaire Basic Info */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-bold mb-4 text-gray-800">Questionnaire Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 mb-2">Questionnaire Name</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={questionnaire.name}
              onChange={(e) => setQuestionnaire({...questionnaire, name: e.target.value})}
              placeholder="E.g., PHQ-9, Anxiety Assessment"
            />
          </div>
          <div>
            <label className="block text-gray-700 mb-2">Banner URL (Optional)</label>
            <input
              type="text"
              className="w-full px-3 py-2 border rounded-md"
              value={questionnaire.banner}
              onChange={(e) => setQuestionnaire({...questionnaire, banner: e.target.value})}
              placeholder="https://example.com/banner.png"
            />
          </div>
        </div>

        {questionnaire.banner && (
          <div className="mt-2 mb-4">
            <p className="text-sm text-gray-600 mb-1">Banner Preview:</p>
            <img
              src={questionnaire.banner}
              alt="Banner Preview"
              className="max-h-24 object-contain border p-2 rounded"
              onError={(e) => e.target.src = "/api/placeholder/400/100"}
            />
          </div>
        )}
      </div>

      {/* Legend Builder */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Legend / Scoring Guide</h2>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
            onClick={() => setEditingLegend(!editingLegend)}
          >
            {editingLegend ? "Hide Legend Editor" : "Edit Legend"}
          </button>
        </div>

        {editingLegend && (
          <div className="border p-4 rounded-md bg-gray-50 mb-4">
            <h3 className="font-semibold mb-2">Headers</h3>
            <div className="flex flex-wrap items-center gap-2 mb-4">
              {questionnaire.legend[0].headers.map((header, index) => (
                <div key={index} className="flex items-center bg-white border rounded px-2 py-1">
                  <input
                    type="text"
                    value={header}
                    onChange={(e) => updateHeaderName(index, e.target.value)}
                    className="border-0 focus:ring-0"
                  />
                  <button
                    onClick={() => removeHeader(index)}
                    className="ml-2 text-red-500 hover:text-red-700"
                  >
                    ×
                  </button>
                </div>
              ))}
              <div className="flex items-center">
                <input
                  type="text"
                  value={newHeaderName}
                  onChange={(e) => setNewHeaderName(e.target.value)}
                  placeholder="New header"
                  className="px-2 py-1 border rounded mr-2"
                />
                <button
                  onClick={addHeader}
                  className="px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  Add
                </button>
              </div>
            </div>

            <h3 className="font-semibold mb-2">Rows</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    {questionnaire.legend[0].headers.map((header, index) => (
                      <th key={index} className="px-3 py-2 bg-gray-100 text-left text-sm font-medium text-gray-700">
                        {header}
                      </th>
                    ))}
                    <th className="px-3 py-2 bg-gray-100"></th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {questionnaire.legend[0].rows.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      {row.map((cell, colIndex) => (
                        <td key={colIndex} className="px-3 py-2">
                          <input
                            type="text"
                            value={cell}
                            onChange={(e) => updateLegendCell(rowIndex, colIndex, e.target.value)}
                            className="w-full px-2 py-1 border rounded"
                          />
                        </td>
                      ))}
                      <td className="px-3 py-2">
                        <button
                          onClick={() => removeLegendRow(rowIndex)}
                          className="text-red-500 hover:text-red-700"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                  {questionnaire.legend[0].headers.length > 0 && (
                    <tr>
                      {questionnaire.legend[0].headers.map((_, colIndex) => (
                        <td key={colIndex} className="px-3 py-2">
                          <input
                            type="text"
                            value={newRowValues[colIndex] || ""}
                            onChange={(e) => {
                              const updatedValues = [...newRowValues];
                              updatedValues[colIndex] = e.target.value;
                              setNewRowValues(updatedValues);
                            }}
                            className="w-full px-2 py-1 border rounded"
                            placeholder={`Value for ${questionnaire.legend[0].headers[colIndex]}`}
                          />
                        </td>
                      ))}
                      <td className="px-3 py-2">
                        <button
                          onClick={addLegendRow}
                          className="px-2 py-1 bg-green-500 text-white rounded hover:bg-green-600"
                        >
                          Add Row
                        </button>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {questionnaire.legend[0].headers.length === 0 && (
              <div className="text-left py-4 text-gray-500 bg-white border rounded">
                <div className="px-3">
                  Add headers first to create rows
                </div>
              </div>
            )}
          </div>
        )}

        {/* Legend Preview */}
        {!editingLegend && questionnaire.legend[0].headers.length > 0 && (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  {questionnaire.legend[0].headers.map((header, index) => (
                    <th key={index} className="px-3 py-2 bg-gray-100 text-left text-sm font-medium text-gray-700">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {questionnaire.legend[0].rows.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {row.map((cell, colIndex) => (
                      <td key={colIndex} className="px-3 py-2 text-sm">{cell}</td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {!editingLegend && questionnaire.legend[0].headers.length === 0 && (
          <div className="text-left py-4 text-gray-500 bg-gray-50 border rounded">
            <div className="px-3">
              No legend defined yet. Click "Edit Legend" to create one.
            </div>
          </div>
        )}
      </div>

      {/* Sections Management */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">Sections</h2>
          <button
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition"
            onClick={addSection}
          >
            Add Section
          </button>
        </div>

        {questionnaire.sections.length === 0 ? (
          <div className="text-left py-8 text-gray-500 bg-gray-50 rounded border">
            <div className="px-4">
              No sections yet. Click "Add Section" to create your first section.
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {questionnaire.sections.map((section, index) => (
              <div
                key={index}
                className={`border rounded-lg overflow-hidden ${currentSection === index ? 'border-blue-500 shadow-md' : 'border-gray-200'}`}
              >
                <div className="flex justify-between items-center bg-gray-50 p-4 border-b">
                  <div className="flex-1">
                    <input
                      type="text"
                      className="w-full px-2 py-1 border rounded"
                      value={section.title}
                      onChange={(e) => updateSectionTitle(index, e.target.value)}
                    />
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      className={`px-3 py-1 rounded ${currentSection === index ? 'bg-blue-100 text-blue-700' : 'bg-gray-200 hover:bg-gray-300'}`}
                      onClick={() => setCurrentSection(currentSection === index ? null : index)}
                    >
                      {currentSection === index ? 'Close' : 'Edit'}
                    </button>
                    <button
                      className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                      onClick={() => deleteSection(index)}
                    >
                      Delete
                    </button>
                  </div>
                </div>

                {currentSection === index && (
                  <div className="p-4">
                    <div className="mb-4">
                      <h3 className="font-medium mb-2">Questions ({section.questions.length})</h3>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {questionTypes.map(type => (
                          <button
                            key={type.id}
                            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
                            onClick={() => addQuestion(index, type.id)}
                          >
                            Add {type.name}
                          </button>
                        ))}
                      </div>
                    </div>

                    {section.questions.length === 0 ? (
                      <div className="text-left py-4 text-gray-500 border-t bg-gray-50 rounded">
                        No questions in this section yet.
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {section.questions.map((question, qIndex) => (
                          <div key={qIndex} className="border rounded-md bg-gray-50 p-4">
                            <div className="mb-3">
                              <div className="flex items-center mb-2">
                                <span className="bg-blue-100 text-blue-800 text-xs font-medium py-1 px-2 rounded mr-2">
                                  {questionTypes.find(t => t.id === question.type)?.name || "Question"}
                                </span>
                                <span className="text-sm text-gray-600">Question Title</span>
                              </div>
                              <textarea
                                className="w-full border rounded px-3 py-2 resize-y min-h-[60px]"
                                value={question.title}
                                onChange={(e) => updateQuestionTitle(index, qIndex, e.target.value)}
                                placeholder="Enter your question text here..."
                                rows="2"
                              />
                              <div className="flex justify-end mt-2">
                                <div className="flex space-x-2">
                                <button
                                  className={`px-2 py-1 rounded text-sm ${editingQuestion && editingQuestion.sectionIndex === index && editingQuestion.questionIndex === qIndex ? 'bg-blue-100 text-blue-700' : 'bg-gray-200 hover:bg-gray-300'}`}
                                  onClick={() => setEditingQuestion(
                                    editingQuestion && editingQuestion.sectionIndex === index && editingQuestion.questionIndex === qIndex
                                      ? null
                                      : { sectionIndex: index, questionIndex: qIndex }
                                  )}
                                >
                                  {editingQuestion && editingQuestion.sectionIndex === index && editingQuestion.questionIndex === qIndex ? 'Done' : 'Options'}
                                </button>
                                <button
                                  className="px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                                  onClick={() => deleteQuestion(index, qIndex)}
                                >
                                  Delete
                                </button>
                                </div>
                              </div>
                            </div>

                            {/* Question Options Editor */}
                            {editingQuestion && editingQuestion.sectionIndex === index && editingQuestion.questionIndex === qIndex && (
                              <div className="mt-3 border-t pt-3">
                                {question.type === "freeform" ? (
                                  <div className="bg-blue-50 p-3 rounded">
                                    <p className="text-sm text-blue-600">
                                      This is a free-form text question. Users will be able to enter their response in a text field.
                                    </p>
                                  </div>
                                ) : (
                                  <>
                                    <h4 className="font-medium mb-2">Options</h4>

                                    {question.questions.length > 0 ? (
                                      <div className="space-y-2 mb-4">
                                        {question.questions.map((option, oIndex) => (
                                          <div key={oIndex} className="bg-white border rounded p-3">
                                            <div className="flex items-start justify-between">
                                              <div className="flex-1 mr-3">
                                                <div className="flex items-center mb-1">
                                                  <span className="mr-2 text-sm font-medium text-gray-500">#{oIndex+1}</span>
                                                  <span className="text-sm text-gray-500">Value: {option.value}</span>
                                                </div>
                                                <div className="text-sm break-words">{option.title}</div>
                                              </div>
                                              <button
                                                className="text-red-500 hover:text-red-700 text-sm font-medium flex-shrink-0"
                                                onClick={() => removeOption(index, qIndex, oIndex)}
                                              >
                                                Remove
                                              </button>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <div className="text-left py-2 text-gray-500 text-sm mb-4 bg-white border rounded p-3">
                                        No options yet. Add some below.
                                      </div>
                                    )}

                                    <div className="bg-gray-100 p-3 rounded">
                                      <h5 className="font-medium text-sm mb-3">Add New Option</h5>
                                      <div className="space-y-3">
                                        <div>
                                          <label className="block text-sm font-medium text-gray-700 mb-1">Option Text</label>
                                          <textarea
                                            className="w-full border rounded px-3 py-2 resize-y min-h-[50px]"
                                            value={newOption.title}
                                            onChange={(e) => setNewOption({...newOption, title: e.target.value})}
                                            placeholder="Enter option text here..."
                                            rows="2"
                                          />
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                          <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Value</label>
                                            <input
                                              type="number"
                                              className="w-full border rounded px-3 py-2"
                                              value={newOption.value}
                                              onChange={(e) => setNewOption({...newOption, value: parseInt(e.target.value) || 0})}
                                              placeholder="Numeric value"
                                            />
                                          </div>
                                          <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">Message (Optional)</label>
                                            <input
                                              type="text"
                                              className="w-full border rounded px-3 py-2"
                                              value={newOption.message}
                                              onChange={(e) => setNewOption({...newOption, message: e.target.value})}
                                              placeholder="Optional message"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <button
                                        className="mt-3 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm font-medium"
                                        onClick={() => addOptionToQuestion(index, qIndex)}
                                      >
                                        Add Option
                                      </button>
                                    </div>
                                  </>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap justify-center gap-4 mb-6">
        <button
          className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
          onClick={saveQuestionnaire}
        >
          Save Questionnaire
        </button>
        <button
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
          onClick={() => setShowPreview(!showPreview)}
        >
          {showPreview ? "Hide JSON" : "Preview JSON"}
        </button>
        {jsonOutput && (
          <button
            className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition"
            onClick={downloadJson}
          >
            Download JSON
          </button>
        )}
        <button
          className="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition"
          onClick={handleImportClick}
        >
          Import JSON
        </button>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".json"
          className="hidden"
        />
        <button
          className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
          onClick={resetQuestionnaire}
        >
          New Questionnaire
        </button>
      </div>

      {/* Import Error Message */}
      {importError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p className="font-bold">Import Error</p>
          <p>{importError}</p>
        </div>
      )}

      {/* JSON Preview */}
      {showPreview && (
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-bold mb-4 text-gray-800">JSON Output</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded-md overflow-x-auto">
            <pre className="text-sm whitespace-pre-wrap">{jsonOutput || "No JSON generated yet. Click 'Save Questionnaire' first."}</pre>
          </div>
        </div>
      )}
    </div>
  );
}